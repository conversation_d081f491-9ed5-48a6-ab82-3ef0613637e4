"""
MongoDB client and connection management for the Agentic RAG application.
Handles MongoDB connections, database operations, and embedding storage.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

import motor.motor_asyncio
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
import numpy as np

logger = logging.getLogger(__name__)


class MongoDBClient:
    """
    MongoDB client for handling document embeddings and metadata storage.
    Supports both sync and async operations.
    """

    def __init__(
        self,
        connection_string: str = "mongodb://localhost:27017",
        database_name: str = "agentic_rag",
        timeout: int = 5000,
    ):
        """
        Initialize MongoDB client.

        Args:
            connection_string: MongoDB connection string
            database_name: Name of the database to use
            timeout: Connection timeout in milliseconds
        """
        self.connection_string = connection_string
        self.database_name = database_name
        self.timeout = timeout
        
        # Sync client for non-async operations
        self._sync_client = None
        self._sync_db = None
        
        # Async client for async operations
        self._async_client = None
        self._async_db = None
        
        # Connection status
        self._is_connected = False
        
        logger.info(f"MongoDB client initialized for database: {database_name}")

    def connect_sync(self) -> bool:
        """
        Establish synchronous connection to MongoDB.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self._sync_client = MongoClient(
                self.connection_string,
                serverSelectionTimeoutMS=self.timeout
            )
            
            # Test connection
            self._sync_client.admin.command('ping')
            self._sync_db = self._sync_client[self.database_name]
            
            logger.info("Successfully connected to MongoDB (sync)")
            return True
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB (sync): {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB (sync): {e}")
            return False

    async def connect_async(self) -> bool:
        """
        Establish asynchronous connection to MongoDB.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self._async_client = motor.motor_asyncio.AsyncIOMotorClient(
                self.connection_string,
                serverSelectionTimeoutMS=self.timeout
            )
            
            # Test connection
            await self._async_client.admin.command('ping')
            self._async_db = self._async_client[self.database_name]
            
            self._is_connected = True
            logger.info("Successfully connected to MongoDB (async)")
            return True
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB (async): {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB (async): {e}")
            return False

    def disconnect_sync(self) -> None:
        """Close synchronous MongoDB connection."""
        if self._sync_client:
            self._sync_client.close()
            self._sync_client = None
            self._sync_db = None
            logger.info("Disconnected from MongoDB (sync)")

    async def disconnect_async(self) -> None:
        """Close asynchronous MongoDB connection."""
        if self._async_client:
            self._async_client.close()
            self._async_client = None
            self._async_db = None
            self._is_connected = False
            logger.info("Disconnected from MongoDB (async)")

    @property
    def sync_db(self):
        """Get synchronous database instance."""
        if not self._sync_db:
            if not self.connect_sync():
                raise ConnectionError("Failed to establish sync connection to MongoDB")
        return self._sync_db

    @property
    def async_db(self):
        """Get asynchronous database instance."""
        if not self._async_db:
            raise ConnectionError("Async connection not established. Call connect_async() first.")
        return self._async_db

    def is_connected(self) -> bool:
        """Check if connected to MongoDB."""
        return self._is_connected

    def get_collection_names(self) -> List[str]:
        """
        Get list of all collection names in the database.
        
        Returns:
            List of collection names
        """
        try:
            return self.sync_db.list_collection_names()
        except Exception as e:
            logger.error(f"Error getting collection names: {e}")
            return []

    async def get_collection_names_async(self) -> List[str]:
        """
        Get list of all collection names in the database (async).
        
        Returns:
            List of collection names
        """
        try:
            return await self.async_db.list_collection_names()
        except Exception as e:
            logger.error(f"Error getting collection names (async): {e}")
            return []

    def create_indexes(self, collection_name: str) -> bool:
        """
        Create necessary indexes for a collection.
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            True if indexes created successfully
        """
        try:
            collection = self.sync_db[collection_name]
            
            # Create text index for content search
            collection.create_index([("content", "text")])
            
            # Create index for metadata fields
            collection.create_index([("metadata.source_file", 1)])
            collection.create_index([("metadata.file_name", 1)])
            collection.create_index([("metadata.processed_at", 1)])
            collection.create_index([("metadata.chunk_index", 1)])
            
            # Create compound index for efficient queries
            collection.create_index([
                ("metadata.source_file", 1),
                ("metadata.chunk_index", 1)
            ])
            
            logger.info(f"Created indexes for collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating indexes for {collection_name}: {e}")
            return False

    async def create_indexes_async(self, collection_name: str) -> bool:
        """
        Create necessary indexes for a collection (async).
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            True if indexes created successfully
        """
        try:
            collection = self.async_db[collection_name]
            
            # Create text index for content search
            await collection.create_index([("content", "text")])
            
            # Create index for metadata fields
            await collection.create_index([("metadata.source_file", 1)])
            await collection.create_index([("metadata.file_name", 1)])
            await collection.create_index([("metadata.processed_at", 1)])
            await collection.create_index([("metadata.chunk_index", 1)])
            
            # Create compound index for efficient queries
            await collection.create_index([
                ("metadata.source_file", 1),
                ("metadata.chunk_index", 1)
            ])
            
            logger.info(f"Created indexes for collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating indexes for {collection_name} (async): {e}")
            return False

    def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """
        Get statistics for a collection.
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            Dictionary containing collection statistics
        """
        try:
            collection = self.sync_db[collection_name]
            stats = self.sync_db.command("collStats", collection_name)
            
            # Get document count
            doc_count = collection.count_documents({})
            
            # Get unique source files
            unique_files = collection.distinct("metadata.source_file")
            
            return {
                "collection_name": collection_name,
                "document_count": doc_count,
                "unique_source_files": len(unique_files),
                "source_files": unique_files,
                "size_bytes": stats.get("size", 0),
                "storage_size_bytes": stats.get("storageSize", 0),
                "avg_obj_size": stats.get("avgObjSize", 0),
                "indexes": stats.get("nindexes", 0),
            }
            
        except Exception as e:
            logger.error(f"Error getting stats for {collection_name}: {e}")
            return {
                "collection_name": collection_name,
                "error": str(e)
            }

    async def get_collection_stats_async(self, collection_name: str) -> Dict[str, Any]:
        """
        Get statistics for a collection (async).
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            Dictionary containing collection statistics
        """
        try:
            collection = self.async_db[collection_name]
            stats = await self.async_db.command("collStats", collection_name)
            
            # Get document count
            doc_count = await collection.count_documents({})
            
            # Get unique source files
            unique_files = await collection.distinct("metadata.source_file")
            
            return {
                "collection_name": collection_name,
                "document_count": doc_count,
                "unique_source_files": len(unique_files),
                "source_files": unique_files,
                "size_bytes": stats.get("size", 0),
                "storage_size_bytes": stats.get("storageSize", 0),
                "avg_obj_size": stats.get("avgObjSize", 0),
                "indexes": stats.get("nindexes", 0),
            }
            
        except Exception as e:
            logger.error(f"Error getting stats for {collection_name} (async): {e}")
            return {
                "collection_name": collection_name,
                "error": str(e)
            }

    def drop_collection(self, collection_name: str) -> bool:
        """
        Drop a collection.
        
        Args:
            collection_name: Name of the collection to drop
            
        Returns:
            True if collection dropped successfully
        """
        try:
            self.sync_db.drop_collection(collection_name)
            logger.info(f"Dropped collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error dropping collection {collection_name}: {e}")
            return False

    async def drop_collection_async(self, collection_name: str) -> bool:
        """
        Drop a collection (async).
        
        Args:
            collection_name: Name of the collection to drop
            
        Returns:
            True if collection dropped successfully
        """
        try:
            await self.async_db.drop_collection(collection_name)
            logger.info(f"Dropped collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error dropping collection {collection_name} (async): {e}")
            return False
