# MongoDB Configuration for Agentic RAG Application
# This file contains MongoDB connection settings and collection configurations

# MongoDB Connection Settings
mongodb:
  # Connection string for MongoDB
  # For Docker: mongodb://mongodb:27017
  # For local development: mongodb://localhost:27017
  connection_string: "mongodb://mongodb:27017"
  
  # Database name
  database_name: "agentic_rag"
  
  # Connection timeout in milliseconds
  timeout: 5000
  
  # Authentication (if required)
  # username: ""
  # password: ""
  # auth_source: "admin"

# Collection Settings
collections:
  # Default collection for document embeddings
  default_embeddings:
    name: "document_embeddings"
    description: "Default collection for storing document embeddings and metadata"
    
  # Academic documents collection
  academic_embeddings:
    name: "academic_embeddings"
    description: "Collection for academic document embeddings"
    
  # Student documents collection
  student_embeddings:
    name: "student_embeddings"
    description: "Collection for student document embeddings"
    
  # Admin documents collection
  admin_embeddings:
    name: "admin_embeddings"
    description: "Collection for admin document embeddings"
    
  # AtlasIQ documents collection
  atlasiq_embeddings:
    name: "atlasiq_embeddings"
    description: "Collection for AtlasIQ document embeddings"

# Embedding Settings
embeddings:
  # OpenAI embedding model to use
  model: "text-embedding-3-small"
  
  # Batch size for processing embeddings
  batch_size: 100
  
  # Similarity search settings
  similarity_search:
    default_top_k: 5
    score_threshold: 0.0
    max_results: 50

# Index Settings
indexes:
  # Whether to create text indexes for full-text search
  create_text_indexes: true
  
  # Whether to create metadata indexes
  create_metadata_indexes: true
  
  # Custom index definitions
  custom_indexes:
    - fields: [["metadata.source_file", 1], ["metadata.chunk_index", 1]]
      name: "source_chunk_idx"
    - fields: [["metadata.file_name", 1]]
      name: "file_name_idx"
    - fields: [["metadata.processed_at", 1]]
      name: "processed_at_idx"

# Storage Settings
storage:
  # Maximum document size in bytes (16MB MongoDB limit)
  max_document_size: 16777216
  
  # Whether to compress embeddings
  compress_embeddings: false
  
  # Retention policy (optional)
  # retention_days: 365

# Performance Settings
performance:
  # Connection pool settings
  max_pool_size: 100
  min_pool_size: 10
  
  # Read/Write concerns
  read_concern: "majority"
  write_concern: "majority"
  
  # Whether to use bulk operations
  use_bulk_operations: true

# Logging Settings
logging:
  # Log MongoDB operations
  log_operations: true
  
  # Log slow queries (threshold in milliseconds)
  slow_query_threshold: 1000
  
  # Log connection events
  log_connections: true
