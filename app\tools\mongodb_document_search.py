"""
MongoDB-based document search tool for the Agentic RAG system.
Provides similarity search and text search capabilities using MongoDB.
"""

import logging
import yaml
from typing import Any, Dict, List, Optional
from pathlib import Path

from langchain_openai import OpenAIEmbeddings

from app.tools.base import BaseTool
from app.database import MongoDBClient, EmbeddingStorage
from app.core.logging_helpers import log_tool_execution

logger = logging.getLogger(__name__)


class MongoDBDocumentSearchTool(BaseTool):
    """Tool for searching documents using MongoDB with vector embeddings."""

    def initialize(self) -> None:
        """Initialize the MongoDB document search tool."""
        # Load MongoDB configuration
        mongodb_config_file = self.config.get("mongodb_config_file", "configs/mongodb_config.yaml")
        self.mongodb_config = self._load_config(mongodb_config_file)
        
        # MongoDB settings
        mongodb_settings = self.mongodb_config.get("mongodb", {})
        self.connection_string = self.config.get("connection_string") or mongodb_settings.get(
            "connection_string", "mongodb://localhost:27017"
        )
        self.database_name = self.config.get("database_name") or mongodb_settings.get(
            "database_name", "agentic_rag"
        )
        
        # Collection and search settings
        self.collection_name = self.config.get("collection_name", "document_embeddings")
        self.embedding_model = self.config.get("embedding_model", "text-embedding-3-small")
        self.top_k = self.config.get("top_k", 5)
        self.score_threshold = self.config.get("score_threshold", 0.0)
        self.search_type = self.config.get("search_type", "similarity")  # 'similarity' or 'text'
        
        # Source type for collection mapping
        self.source_type = self.config.get("source_type", "default")

        # Initialize MongoDB client
        try:
            self.mongodb_client = MongoDBClient(
                connection_string=self.connection_string,
                database_name=self.database_name,
                timeout=mongodb_settings.get("timeout", 5000)
            )
            
            # Initialize embeddings
            self.embeddings = OpenAIEmbeddings(model=self.embedding_model)
            
            # Initialize embedding storage
            self.embedding_storage = EmbeddingStorage(
                mongodb_client=self.mongodb_client,
                embedding_model=self.embedding_model,
                collection_name=self.collection_name
            )
            
            logger.info(f"Initialized MongoDB document search tool")
            logger.info(f"Collection: {self.collection_name}")
            logger.info(f"Search type: {self.search_type}")
            
        except Exception as e:
            logger.error(f"Error initializing MongoDB document search tool: {str(e)}")
            self.mongodb_client = None
            self.embedding_storage = None

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        config_path = Path(config_file)
        if not config_path.exists():
            logger.warning(f"Config file not found: {config_file}, using defaults")
            return {}

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                logger.debug(f"Loaded configuration from: {config_file}")
                return config or {}
        except Exception as e:
            logger.error(f"Error loading config from {config_file}: {e}")
            return {}

    def _get_collection_name(self, source_type: str = None) -> str:
        """Get MongoDB collection name based on source type."""
        if source_type is None:
            source_type = self.source_type
            
        collection_mapping = {
            "default": "document_embeddings",
            "academic": "academic_embeddings",
            "student": "student_embeddings",
            "admin": "admin_embeddings",
            "atlasiq": "atlasiq_embeddings",
        }
        
        # Check if custom collection names are defined in config
        collections_config = self.mongodb_config.get("collections", {})
        if source_type in collections_config:
            return collections_config[source_type].get("name", collection_mapping.get(source_type, "document_embeddings"))
        
        return collection_mapping.get(source_type, "document_embeddings")

    @log_tool_execution
    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for documents relevant to the query.

        Args:
            query: The user query
            **kwargs: Additional arguments including:
                - top_k: Number of results to return
                - source_type: Type of documents to search ('default', 'academic', 'student', 'admin', 'atlasiq')
                - collection_name: Specific collection name (overrides source_type)
                - search_type: Type of search ('similarity' or 'text')
                - score_threshold: Minimum similarity score threshold
                - filter_dict: Metadata filter dictionary

        Returns:
            A dictionary containing the search results
        """
        if not self.mongodb_client or not self.embedding_storage:
            return {
                "success": False,
                "error": "MongoDB client not initialized",
                "documents": [],
            }

        try:
            # Ensure MongoDB connection
            if not self.mongodb_client.is_connected():
                await self.mongodb_client.connect_async()

            # Get search parameters
            top_k = kwargs.get("top_k", self.top_k)
            search_type = kwargs.get("search_type", self.search_type)
            score_threshold = kwargs.get("score_threshold", self.score_threshold)
            filter_dict = kwargs.get("filter_dict", None)
            
            # Determine collection name
            collection_name = kwargs.get("collection_name")
            if not collection_name:
                source_type = kwargs.get("source_type", self.source_type)
                collection_name = self._get_collection_name(source_type)

            # Update embedding storage collection if different
            if collection_name != self.embedding_storage.collection_name:
                self.embedding_storage = EmbeddingStorage(
                    mongodb_client=self.mongodb_client,
                    embedding_model=self.embedding_model,
                    collection_name=collection_name
                )

            # Perform search based on type
            if search_type == "similarity":
                results = await self.embedding_storage.similarity_search(
                    query=query,
                    top_k=top_k,
                    filter_dict=filter_dict,
                    score_threshold=score_threshold
                )
            elif search_type == "text":
                results = await self.embedding_storage.text_search(
                    query=query,
                    top_k=top_k,
                    filter_dict=filter_dict
                )
            else:
                return {
                    "success": False,
                    "error": f"Invalid search type: {search_type}. Use 'similarity' or 'text'.",
                    "documents": [],
                }

            # Format results for compatibility with existing code
            formatted_results = []
            for result in results:
                formatted_result = {
                    "content": result.get("content", ""),
                    "metadata": result.get("metadata", {}),
                }
                
                # Add score if available
                if "similarity_score" in result:
                    formatted_result["score"] = result["similarity_score"]
                elif "score" in result:
                    formatted_result["score"] = result["score"]
                
                formatted_results.append(formatted_result)

            return {
                "success": True,
                "documents": formatted_results,
                "count": len(formatted_results),
                "query": query,
                "collection_name": collection_name,
                "search_type": search_type,
                "parameters": {
                    "top_k": top_k,
                    "score_threshold": score_threshold,
                    "filter_dict": filter_dict,
                }
            }

        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "documents": [],
            }

    async def search_by_source_type(
        self,
        query: str,
        source_type: str,
        top_k: int = None,
        search_type: str = None,
        score_threshold: float = None,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Search documents by source type.

        Args:
            query: Search query
            source_type: Type of documents to search
            top_k: Number of results to return
            search_type: Type of search ('similarity' or 'text')
            score_threshold: Minimum similarity score threshold
            filter_dict: Metadata filter dictionary

        Returns:
            Dictionary containing search results
        """
        return await self.execute(
            query=query,
            source_type=source_type,
            top_k=top_k or self.top_k,
            search_type=search_type or self.search_type,
            score_threshold=score_threshold or self.score_threshold,
            filter_dict=filter_dict
        )

    async def search_multiple_collections(
        self,
        query: str,
        source_types: List[str],
        top_k_per_collection: int = None,
        search_type: str = None,
        score_threshold: float = None,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Search across multiple collections/source types.

        Args:
            query: Search query
            source_types: List of source types to search
            top_k_per_collection: Number of results per collection
            search_type: Type of search ('similarity' or 'text')
            score_threshold: Minimum similarity score threshold
            filter_dict: Metadata filter dictionary

        Returns:
            Dictionary containing aggregated search results
        """
        all_results = []
        collection_results = {}
        
        top_k = top_k_per_collection or self.top_k
        search_type = search_type or self.search_type
        score_threshold = score_threshold or self.score_threshold

        for source_type in source_types:
            try:
                result = await self.search_by_source_type(
                    query=query,
                    source_type=source_type,
                    top_k=top_k,
                    search_type=search_type,
                    score_threshold=score_threshold,
                    filter_dict=filter_dict
                )
                
                if result["success"]:
                    collection_results[source_type] = result
                    # Add source_type to each document's metadata
                    for doc in result["documents"]:
                        doc["metadata"]["search_source_type"] = source_type
                    all_results.extend(result["documents"])
                else:
                    collection_results[source_type] = result
                    logger.warning(f"Search failed for source type {source_type}: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"Error searching source type {source_type}: {e}")
                collection_results[source_type] = {
                    "success": False,
                    "error": str(e),
                    "documents": []
                }

        # Sort all results by score if available
        if search_type == "similarity" and all_results:
            all_results.sort(key=lambda x: x.get("score", 0), reverse=True)

        return {
            "success": True,
            "documents": all_results,
            "count": len(all_results),
            "query": query,
            "source_types": source_types,
            "search_type": search_type,
            "collection_results": collection_results,
            "parameters": {
                "top_k_per_collection": top_k,
                "score_threshold": score_threshold,
                "filter_dict": filter_dict,
            }
        }

    async def get_collection_info(self, source_type: str = None) -> Dict[str, Any]:
        """
        Get information about a collection.

        Args:
            source_type: Source type to get info for

        Returns:
            Dictionary containing collection information
        """
        try:
            if not self.mongodb_client:
                return {"success": False, "error": "MongoDB client not initialized"}

            collection_name = self._get_collection_name(source_type or self.source_type)
            
            embedding_storage = EmbeddingStorage(
                mongodb_client=self.mongodb_client,
                embedding_model=self.embedding_model,
                collection_name=collection_name
            )
            
            return await embedding_storage.get_collection_info()
            
        except Exception as e:
            logger.error(f"Error getting collection info: {e}")
            return {"success": False, "error": str(e)}

    async def close(self):
        """Close MongoDB connections."""
        if self.mongodb_client:
            await self.mongodb_client.disconnect_async()
            self.mongodb_client.disconnect_sync()

    @classmethod
    def get_tool_description(cls) -> str:
        return "Searches for documents relevant to a query using MongoDB with vector embeddings and text search capabilities."
