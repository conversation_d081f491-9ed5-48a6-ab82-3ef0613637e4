services:
  # MongoDB Database Service
  mongodb:
    image: mongo:7.0
    container_name: agentic-rag-mongodb-dev
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=agentic_rag
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - agentic-rag-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Agentic RAG Backend
  agentic-rag:
    build:
      # The build context is now the parent directory (project root)
      # so that the Dockerfile can find all necessary files (app, configs, etc.).
      context: ..
      # The Dockerfile path is relative to the build context (project root).
      # It should point to the file inside the 'docker' directory.
      dockerfile: docker/Dockerfile.backend-only
      target: production
    container_name: agentic-rag-backend
    restart: unless-stopped
    # Port mapping for backend service
    ports:
      - "3820:3820"
    environment:
      - HOST=0.0.0.0
      - PORT=3820
      - DEBUG=true
      - LOG_LEVEL=INFO
      - LOG_DIR=logs
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      # MongoDB connection settings
      - MONGODB_CONNECTION_STRING=mongodb://mongodb:27017
      - MONGODB_DATABASE=agentic_rag
      - MONGODB_USERNAME=admin
      - MONGODB_PASSWORD=password
    volumes:
      # Volume paths must now point to the parent directory.
      - ../data:/app/data
      - ../configs:/app/configs:ro
      - ../prompts:/app/prompts:ro
      - ../logs:/app/logs
    # Use custom network instead of host mode for MongoDB connectivity
    networks:
      - agentic-rag-network
    depends_on:
      mongodb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3820/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Docker volumes for persistent data
volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

# Docker networks
networks:
  agentic-rag-network:
    driver: bridge
