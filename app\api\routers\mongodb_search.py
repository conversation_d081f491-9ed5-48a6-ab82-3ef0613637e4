"""
MongoDB-based document search API endpoints.
"""

import os
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel

from app.document_processing.mongodb_document_processor import MongoDBDocumentProcessor
from app.tools.mongodb_document_search import MongoDBDocumentSearchTool
from app.core.logging_config import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/mongodb/search", tags=["MongoDB Search"])


# Pydantic models for request/response
class SearchRequest(BaseModel):
    query: str
    collection_name: Optional[str] = None
    source_type: str = "default"
    top_k: int = 5
    search_type: str = "similarity"  # "similarity" or "text"
    score_threshold: float = 0.0
    filter_dict: Optional[Dict[str, Any]] = None


class SearchResponse(BaseModel):
    success: bool
    query: str
    collection_name: str
    search_type: str
    results: List[Dict[str, Any]]
    count: int
    error: Optional[str] = None


class MultiSearchRequest(BaseModel):
    query: str
    source_types: List[str]
    top_k_per_collection: int = 5
    search_type: str = "similarity"
    score_threshold: float = 0.0
    filter_dict: Optional[Dict[str, Any]] = None


class CollectionInfoResponse(BaseModel):
    success: bool
    collection_name: str
    document_count: int
    unique_source_files: int
    source_files: List[str]
    size_bytes: int
    error: Optional[str] = None


def get_mongodb_processor() -> MongoDBDocumentProcessor:
    """Get MongoDB document processor instance."""
    connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017")
    database_name = os.getenv("MONGODB_DATABASE", "agentic_rag")
    
    return MongoDBDocumentProcessor(
        connection_string=connection_string,
        database_name=database_name,
        embedding_model="text-embedding-3-small"
    )


def get_mongodb_search_tool() -> MongoDBDocumentSearchTool:
    """Get MongoDB search tool instance."""
    connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017")
    database_name = os.getenv("MONGODB_DATABASE", "agentic_rag")
    
    search_tool = MongoDBDocumentSearchTool()
    search_tool.config = {
        "connection_string": connection_string,
        "database_name": database_name,
        "collection_name": "document_embeddings",
        "embedding_model": "text-embedding-3-small",
        "top_k": 5,
        "search_type": "similarity"
    }
    search_tool.initialize()
    return search_tool


@router.post("/search", response_model=SearchResponse)
async def search_documents_mongodb(
    request: SearchRequest,
    search_tool: MongoDBDocumentSearchTool = Depends(get_mongodb_search_tool),
):
    """Search documents using MongoDB similarity or text search."""
    
    try:
        logger.info(f"MongoDB search request: {request.query}")
        logger.info(f"Search type: {request.search_type}, Source type: {request.source_type}")
        
        result = await search_tool.execute(
            query=request.query,
            collection_name=request.collection_name,
            source_type=request.source_type,
            top_k=request.top_k,
            search_type=request.search_type,
            score_threshold=request.score_threshold,
            filter_dict=request.filter_dict,
        )
        
        if result["success"]:
            return SearchResponse(
                success=True,
                query=request.query,
                collection_name=result["collection_name"],
                search_type=result["search_type"],
                results=result["documents"],
                count=result["count"],
            )
        else:
            logger.error(f"MongoDB search failed: {result.get('error', 'Unknown error')}")
            return SearchResponse(
                success=False,
                query=request.query,
                collection_name=request.collection_name or "unknown",
                search_type=request.search_type,
                results=[],
                count=0,
                error=result.get("error", "Unknown error"),
            )
    
    except Exception as e:
        error_msg = f"Error during MongoDB search: {str(e)}"
        logger.error(error_msg)
        import traceback
        logger.error(f"MongoDB search traceback: {traceback.format_exc()}")
        
        return SearchResponse(
            success=False,
            query=request.query,
            collection_name=request.collection_name or "unknown",
            search_type=request.search_type,
            results=[],
            count=0,
            error=error_msg,
        )
    
    finally:
        await search_tool.close()


@router.post("/search-multiple", response_model=Dict[str, Any])
async def search_multiple_collections_mongodb(
    request: MultiSearchRequest,
    search_tool: MongoDBDocumentSearchTool = Depends(get_mongodb_search_tool),
):
    """Search across multiple collections/source types."""
    
    try:
        logger.info(f"MongoDB multi-search request: {request.query}")
        logger.info(f"Source types: {request.source_types}")
        
        result = await search_tool.search_multiple_collections(
            query=request.query,
            source_types=request.source_types,
            top_k_per_collection=request.top_k_per_collection,
            search_type=request.search_type,
            score_threshold=request.score_threshold,
            filter_dict=request.filter_dict,
        )
        
        return result
    
    except Exception as e:
        error_msg = f"Error during MongoDB multi-search: {str(e)}"
        logger.error(error_msg)
        import traceback
        logger.error(f"MongoDB multi-search traceback: {traceback.format_exc()}")
        
        return {
            "success": False,
            "error": error_msg,
            "documents": [],
            "count": 0,
        }
    
    finally:
        await search_tool.close()


@router.get("/collection-info/{source_type}", response_model=CollectionInfoResponse)
async def get_collection_info_mongodb(
    source_type: str,
    processor: MongoDBDocumentProcessor = Depends(get_mongodb_processor),
):
    """Get information about a collection."""
    
    try:
        logger.info(f"Getting MongoDB collection info for source type: {source_type}")
        
        result = await processor.get_collection_info(source_type=source_type)
        
        if "error" not in result:
            return CollectionInfoResponse(
                success=True,
                collection_name=result["collection_name"],
                document_count=result["document_count"],
                unique_source_files=result["unique_source_files"],
                source_files=result["source_files"],
                size_bytes=result.get("size_bytes", 0),
            )
        else:
            return CollectionInfoResponse(
                success=False,
                collection_name=result.get("collection_name", "unknown"),
                document_count=0,
                unique_source_files=0,
                source_files=[],
                size_bytes=0,
                error=result["error"],
            )
    
    except Exception as e:
        error_msg = f"Error getting MongoDB collection info: {str(e)}"
        logger.error(error_msg)
        
        return CollectionInfoResponse(
            success=False,
            collection_name="unknown",
            document_count=0,
            unique_source_files=0,
            source_files=[],
            size_bytes=0,
            error=error_msg,
        )
    
    finally:
        await processor.close()


@router.delete("/collection/{source_type}")
async def delete_collection_mongodb(
    source_type: str,
    processor: MongoDBDocumentProcessor = Depends(get_mongodb_processor),
):
    """Delete a collection."""
    
    try:
        logger.info(f"Deleting MongoDB collection for source type: {source_type}")
        
        result = await processor.delete_collection(source_type=source_type)
        
        return result
    
    except Exception as e:
        error_msg = f"Error deleting MongoDB collection: {str(e)}"
        logger.error(error_msg)
        
        return {
            "success": False,
            "error": error_msg,
        }
    
    finally:
        await processor.close()


@router.get("/processing-status")
async def get_processing_status_mongodb(
    file_path: Optional[str] = Query(None, description="Specific file path to check"),
    processor: MongoDBDocumentProcessor = Depends(get_mongodb_processor),
):
    """Get processing status for files."""
    
    try:
        logger.info("Getting MongoDB processing status")
        
        status = processor.get_processing_status(file_path)
        
        return {
            "success": True,
            "status": status,
        }
    
    except Exception as e:
        error_msg = f"Error getting MongoDB processing status: {str(e)}"
        logger.error(error_msg)
        
        return {
            "success": False,
            "error": error_msg,
        }
    
    finally:
        await processor.close()


@router.get("/source-directories")
async def get_source_directories_mongodb(
    processor: MongoDBDocumentProcessor = Depends(get_mongodb_processor),
):
    """Get all configured source directories."""
    
    try:
        directories = processor.get_source_directories()
        
        return {
            "success": True,
            "directories": directories,
        }
    
    except Exception as e:
        error_msg = f"Error getting source directories: {str(e)}"
        logger.error(error_msg)
        
        return {
            "success": False,
            "error": error_msg,
        }
    
    finally:
        await processor.close()
