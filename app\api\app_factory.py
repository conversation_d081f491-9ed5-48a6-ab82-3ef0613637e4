"""
FastAPI application factory.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.dependencies import initialize_dependencies
from app.api.middleware import log_requests
from app.api.routers import (
    bots,
    documents,
    database,
    admin,
    mongodb_documents,
    mongodb_search,
)
from app.core.logging_config import get_logger

logger = get_logger(__name__)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""

    # Initialize dependencies
    initialize_dependencies()

    # Create the FastAPI application
    app = FastAPI(
        title="Agentic RAG API",
        description="API for the Agentic RAG system",
        version="0.1.0",
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add request logging middleware
    app.middleware("http")(log_requests)

    # Include routers
    app.include_router(bots.router)
    app.include_router(documents.router)
    app.include_router(database.router)
    app.include_router(admin.router)

    # Include MongoDB routers
    app.include_router(mongodb_documents.router)
    app.include_router(mongodb_search.router)

    # Health check endpoint
    @app.get("/", tags=["Health"])
    async def root():
        """Root endpoint for health check."""
        return {"status": "ok", "message": "Agentic RAG API is running"}

    logger.info("FastAPI application created and configured successfully")

    return app
