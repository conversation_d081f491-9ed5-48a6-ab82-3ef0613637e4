"""
MongoDB-based document processing API endpoints.
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, Form, HTTPException, Query
from pydantic import BaseModel

from app.document_processing.mongodb_document_processor import MongoDBDocumentProcessor
from app.tools.mongodb_document_search import MongoDBDocumentSearchTool
from app.models.api_models import DocumentUploadResponse
from app.core.logging_config import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/mongodb/documents", tags=["MongoDB Documents"])


# Pydantic models for request/response
class SearchRequest(BaseModel):
    query: str
    collection_name: Optional[str] = None
    source_type: str = "default"
    top_k: int = 5
    search_type: str = "similarity"  # "similarity" or "text"
    score_threshold: float = 0.0
    filter_dict: Optional[Dict[str, Any]] = None


class SearchResponse(BaseModel):
    success: bool
    query: str
    collection_name: str
    search_type: str
    results: List[Dict[str, Any]]
    count: int
    error: Optional[str] = None


class MultiSearchRequest(BaseModel):
    query: str
    source_types: List[str]
    top_k_per_collection: int = 5
    search_type: str = "similarity"
    score_threshold: float = 0.0
    filter_dict: Optional[Dict[str, Any]] = None


class CollectionInfoResponse(BaseModel):
    success: bool
    collection_name: str
    document_count: int
    unique_source_files: int
    source_files: List[str]
    size_bytes: int
    error: Optional[str] = None


def get_mongodb_processor() -> MongoDBDocumentProcessor:
    """Get MongoDB document processor instance."""
    connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017")
    database_name = os.getenv("MONGODB_DATABASE", "agentic_rag")
    
    return MongoDBDocumentProcessor(
        connection_string=connection_string,
        database_name=database_name,
        embedding_model="text-embedding-3-small"
    )


def get_mongodb_search_tool() -> MongoDBDocumentSearchTool:
    """Get MongoDB search tool instance."""
    connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017")
    database_name = os.getenv("MONGODB_DATABASE", "agentic_rag")
    
    search_tool = MongoDBDocumentSearchTool()
    search_tool.config = {
        "connection_string": connection_string,
        "database_name": database_name,
        "collection_name": "document_embeddings",
        "embedding_model": "text-embedding-3-small",
        "top_k": 5,
        "search_type": "similarity"
    }
    search_tool.initialize()
    return search_tool


@router.post("/process-directory", response_model=DocumentUploadResponse)
async def process_directory_mongodb(
    collection_name: Optional[str] = Form(None, description="Name of the collection (optional)"),
    source_type: str = Form("default", description="Source type (default, academic, student, admin, atlasiq)"),
    directory_path: str = Form(..., description="Path to the directory containing documents"),
    recursive: bool = Form(True, description="Whether to process subdirectories recursively"),
    processor: MongoDBDocumentProcessor = Depends(get_mongodb_processor),
):
    """Process documents from a directory using MongoDB storage."""
    
    try:
        logger.info(f"Starting MongoDB directory processing for source_type: {source_type}")
        logger.info(f"Directory path: {directory_path}")
        
        # Validate directory path
        dir_path = Path(directory_path)
        if not dir_path.exists():
            return DocumentUploadResponse(
                success=False,
                message="Directory does not exist",
                error=f"The specified directory path does not exist: {directory_path}",
            )
        
        if not dir_path.is_dir():
            return DocumentUploadResponse(
                success=False,
                message="Path is not a directory",
                error=f"The specified path is not a directory: {directory_path}",
            )
        
        # Get list of files
        files_in_dir = []
        if recursive:
            files_in_dir = [f.name for f in dir_path.rglob("*") if f.is_file()]
        else:
            files_in_dir = [f.name for f in dir_path.iterdir() if f.is_file()]
        
        logger.info(f"Found {len(files_in_dir)} files to process")
        
        # Process the directory
        custom_metadata = {
            "source_type": source_type,
            "processed_by": "mongodb_api",
            "processing_timestamp": datetime.now().isoformat(),
            "source_directory": str(dir_path.resolve()),
        }
        
        result = await processor.process_directory(
            directory_path=directory_path,
            collection_name=collection_name,
            source_type=source_type,
            custom_metadata=custom_metadata,
            recursive=recursive,
        )
        
        if result["success"]:
            message = f"Successfully processed directory: {directory_path}"
            if result["failed_count"] > 0:
                message += f" ({result['failed_count']} files failed to process)"
            
            logger.info(f"MongoDB directory processing completed: {message}")
            
            return DocumentUploadResponse(
                success=True,
                message=message,
                collection_name=result["collection_name"],
                uploaded_files=files_in_dir,
                processed_files=result["successful_count"],
                failed_files=result["failed_count"],
                temp_directory=directory_path,
                details={
                    "total_files": result["total_files"],
                    "source_type": result["source_type"],
                    "processing_results": result["results"],
                    "source_directory": str(dir_path.resolve()),
                    "recursive": recursive,
                },
            )
        else:
            logger.error(f"MongoDB directory processing failed: {result.get('error', 'Unknown error')}")
            return DocumentUploadResponse(
                success=False,
                message="Directory processing failed",
                collection_name=collection_name or f"{source_type}_embeddings",
                uploaded_files=files_in_dir,
                processed_files=0,
                failed_files=len(files_in_dir),
                temp_directory=directory_path,
                error=result.get("error", "Unknown error"),
            )
    
    except Exception as e:
        error_msg = f"Error during MongoDB directory processing: {str(e)}"
        logger.error(error_msg)
        import traceback
        logger.error(f"MongoDB directory processing traceback: {traceback.format_exc()}")
        
        return DocumentUploadResponse(
            success=False,
            message="Directory processing failed",
            collection_name=collection_name or f"{source_type}_embeddings",
            error=error_msg,
        )
    
    finally:
        await processor.close()


@router.post("/process-source-type", response_model=DocumentUploadResponse)
async def process_source_type_mongodb(
    source_type: str = Form(..., description="Source type (default, academic, student, admin, atlasiq)"),
    collection_name: Optional[str] = Form(None, description="Name of the collection (optional)"),
    processor: MongoDBDocumentProcessor = Depends(get_mongodb_processor),
):
    """Process documents from a source type directory using MongoDB storage."""
    
    try:
        logger.info(f"Starting MongoDB source type processing: {source_type}")
        
        # Get source directories
        source_dirs = processor.get_source_directories()
        if source_type not in source_dirs:
            return DocumentUploadResponse(
                success=False,
                message="Invalid source type",
                error=f"Invalid source type: {source_type}. Valid types: {list(source_dirs.keys())}",
            )
        
        source_dir = source_dirs[source_type]
        logger.info(f"Processing source directory: {source_dir}")
        
        # Custom metadata
        custom_metadata = {
            "source_type": source_type,
            "processed_by": "mongodb_api",
            "processing_timestamp": datetime.now().isoformat(),
        }
        
        result = await processor.process_source_directory_by_type(
            source_type=source_type,
            collection_name=collection_name,
            custom_metadata=custom_metadata,
        )
        
        if result["success"]:
            message = f"Successfully processed {source_type} documents"
            if result["failed_count"] > 0:
                message += f" ({result['failed_count']} files failed to process)"
            
            logger.info(f"MongoDB source type processing completed: {message}")
            
            return DocumentUploadResponse(
                success=True,
                message=message,
                collection_name=result["collection_name"],
                processed_files=result["successful_count"],
                failed_files=result["failed_count"],
                temp_directory=source_dir,
                details={
                    "total_files": result["total_files"],
                    "source_type": result["source_type"],
                    "processing_results": result["results"],
                    "source_directory": source_dir,
                },
            )
        else:
            logger.error(f"MongoDB source type processing failed: {result.get('error', 'Unknown error')}")
            return DocumentUploadResponse(
                success=False,
                message="Source type processing failed",
                collection_name=collection_name or f"{source_type}_embeddings",
                processed_files=0,
                failed_files=0,
                temp_directory=source_dir,
                error=result.get("error", "Unknown error"),
            )
    
    except Exception as e:
        error_msg = f"Error during MongoDB source type processing: {str(e)}"
        logger.error(error_msg)
        import traceback
        logger.error(f"MongoDB source type processing traceback: {traceback.format_exc()}")
        
        return DocumentUploadResponse(
            success=False,
            message="Source type processing failed",
            collection_name=collection_name or f"{source_type}_embeddings",
            error=error_msg,
        )
    
    finally:
        await processor.close()
