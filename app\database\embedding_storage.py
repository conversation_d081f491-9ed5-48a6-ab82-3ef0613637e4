"""
Embedding storage and retrieval system for MongoDB.
Handles document embeddings, metadata, and similarity search operations.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import numpy as np

from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings

from .mongodb_client import MongoDBClient

logger = logging.getLogger(__name__)


class EmbeddingStorage:
    """
    Handles storage and retrieval of document embeddings in MongoDB.
    Provides similarity search functionality using cosine similarity.
    """

    def __init__(
        self,
        mongodb_client: MongoDBClient,
        embedding_model: str = "text-embedding-3-small",
        collection_name: str = "document_embeddings",
    ):
        """
        Initialize embedding storage.

        Args:
            mongodb_client: MongoDB client instance
            embedding_model: OpenAI embedding model to use
            collection_name: Name of the MongoDB collection for embeddings
        """
        self.mongodb_client = mongodb_client
        self.collection_name = collection_name
        self.embeddings = OpenAIEmbeddings(model=embedding_model)
        
        logger.info(f"EmbeddingStorage initialized with model: {embedding_model}")
        logger.info(f"Using collection: {collection_name}")

    async def store_documents(
        self,
        documents: List[Document],
        batch_size: int = 100
    ) -> Dict[str, Any]:
        """
        Store documents with their embeddings in MongoDB.

        Args:
            documents: List of LangChain Document objects
            batch_size: Number of documents to process in each batch

        Returns:
            Dictionary containing storage results
        """
        if not documents:
            return {"success": False, "error": "No documents provided"}

        try:
            # Ensure async connection
            if not self.mongodb_client.is_connected():
                await self.mongodb_client.connect_async()

            collection = self.mongodb_client.async_db[self.collection_name]
            
            # Create indexes if they don't exist
            await self.mongodb_client.create_indexes_async(self.collection_name)

            total_stored = 0
            failed_count = 0
            
            # Process documents in batches
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                batch_results = await self._store_batch(collection, batch)
                
                total_stored += batch_results["stored"]
                failed_count += batch_results["failed"]
                
                logger.info(f"Processed batch {i//batch_size + 1}: "
                          f"{batch_results['stored']} stored, {batch_results['failed']} failed")

            result = {
                "success": True,
                "total_documents": len(documents),
                "stored_count": total_stored,
                "failed_count": failed_count,
                "collection_name": self.collection_name,
            }

            logger.info(f"Document storage completed: {total_stored} stored, {failed_count} failed")
            return result

        except Exception as e:
            error_msg = f"Error storing documents: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def _store_batch(
        self,
        collection,
        documents: List[Document]
    ) -> Dict[str, int]:
        """
        Store a batch of documents with embeddings.

        Args:
            collection: MongoDB collection instance
            documents: List of documents to store

        Returns:
            Dictionary with stored and failed counts
        """
        stored_count = 0
        failed_count = 0

        try:
            # Extract text content for embedding
            texts = [doc.page_content for doc in documents]
            
            # Generate embeddings for the batch
            embeddings = await self.embeddings.aembed_documents(texts)
            
            # Prepare documents for insertion
            docs_to_insert = []
            for doc, embedding in zip(documents, embeddings):
                doc_dict = {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "embedding": embedding,
                    "stored_at": datetime.now().isoformat(),
                    "embedding_model": self.embeddings.model,
                }
                docs_to_insert.append(doc_dict)

            # Insert batch into MongoDB
            if docs_to_insert:
                result = await collection.insert_many(docs_to_insert)
                stored_count = len(result.inserted_ids)
                
        except Exception as e:
            logger.error(f"Error storing batch: {e}")
            failed_count = len(documents)

        return {"stored": stored_count, "failed": failed_count}

    async def similarity_search(
        self,
        query: str,
        top_k: int = 5,
        filter_dict: Optional[Dict[str, Any]] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        Perform similarity search using embeddings.

        Args:
            query: Search query text
            top_k: Number of results to return
            filter_dict: Optional metadata filter
            score_threshold: Minimum similarity score threshold

        Returns:
            List of search results with content, metadata, and scores
        """
        try:
            # Ensure async connection
            if not self.mongodb_client.is_connected():
                await self.mongodb_client.connect_async()

            collection = self.mongodb_client.async_db[self.collection_name]

            # Generate embedding for query
            query_embedding = await self.embeddings.aembed_query(query)

            # Build aggregation pipeline for similarity search
            pipeline = []

            # Add match stage if filter provided
            if filter_dict:
                match_conditions = {}
                for key, value in filter_dict.items():
                    match_conditions[f"metadata.{key}"] = value
                pipeline.append({"$match": match_conditions})

            # Add vector search stage using $addFields and $sort
            pipeline.extend([
                {
                    "$addFields": {
                        "similarity_score": {
                            "$let": {
                                "vars": {
                                    "dot_product": {
                                        "$reduce": {
                                            "input": {"$range": [0, {"$size": "$embedding"}]},
                                            "initialValue": 0,
                                            "in": {
                                                "$add": [
                                                    "$$value",
                                                    {
                                                        "$multiply": [
                                                            {"$arrayElemAt": ["$embedding", "$$this"]},
                                                            {"$arrayElemAt": [query_embedding, "$$this"]}
                                                        ]
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                },
                                "in": "$$dot_product"
                            }
                        }
                    }
                },
                {"$match": {"similarity_score": {"$gte": score_threshold}}},
                {"$sort": {"similarity_score": -1}},
                {"$limit": top_k},
                {
                    "$project": {
                        "content": 1,
                        "metadata": 1,
                        "similarity_score": 1,
                        "stored_at": 1,
                        "_id": 0
                    }
                }
            ])

            # Execute aggregation
            cursor = collection.aggregate(pipeline)
            results = await cursor.to_list(length=top_k)

            logger.info(f"Similarity search completed: {len(results)} results found")
            return results

        except Exception as e:
            error_msg = f"Error performing similarity search: {str(e)}"
            logger.error(error_msg)
            return []

    async def get_documents_by_source(
        self,
        source_file: str,
        include_embeddings: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Retrieve all documents from a specific source file.

        Args:
            source_file: Path to the source file
            include_embeddings: Whether to include embedding vectors

        Returns:
            List of documents from the source file
        """
        try:
            if not self.mongodb_client.is_connected():
                await self.mongodb_client.connect_async()

            collection = self.mongodb_client.async_db[self.collection_name]

            # Build projection
            projection = {
                "content": 1,
                "metadata": 1,
                "stored_at": 1,
                "_id": 0
            }
            
            if include_embeddings:
                projection["embedding"] = 1

            # Query documents
            cursor = collection.find(
                {"metadata.source_file": source_file},
                projection
            ).sort("metadata.chunk_index", 1)

            results = await cursor.to_list(length=None)
            
            logger.info(f"Retrieved {len(results)} documents from source: {source_file}")
            return results

        except Exception as e:
            error_msg = f"Error retrieving documents by source: {str(e)}"
            logger.error(error_msg)
            return []

    async def delete_documents_by_source(self, source_file: str) -> Dict[str, Any]:
        """
        Delete all documents from a specific source file.

        Args:
            source_file: Path to the source file

        Returns:
            Dictionary containing deletion results
        """
        try:
            if not self.mongodb_client.is_connected():
                await self.mongodb_client.connect_async()

            collection = self.mongodb_client.async_db[self.collection_name]

            # Delete documents
            result = await collection.delete_many({"metadata.source_file": source_file})

            logger.info(f"Deleted {result.deleted_count} documents from source: {source_file}")
            
            return {
                "success": True,
                "deleted_count": result.deleted_count,
                "source_file": source_file
            }

        except Exception as e:
            error_msg = f"Error deleting documents by source: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def get_collection_info(self) -> Dict[str, Any]:
        """
        Get information about the embedding collection.

        Returns:
            Dictionary containing collection information
        """
        try:
            return await self.mongodb_client.get_collection_stats_async(self.collection_name)
        except Exception as e:
            error_msg = f"Error getting collection info: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    async def text_search(
        self,
        query: str,
        top_k: int = 5,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform text-based search using MongoDB text index.

        Args:
            query: Search query text
            top_k: Number of results to return
            filter_dict: Optional metadata filter

        Returns:
            List of search results
        """
        try:
            if not self.mongodb_client.is_connected():
                await self.mongodb_client.connect_async()

            collection = self.mongodb_client.async_db[self.collection_name]

            # Build query
            search_query = {"$text": {"$search": query}}
            
            if filter_dict:
                for key, value in filter_dict.items():
                    search_query[f"metadata.{key}"] = value

            # Execute search with text score
            cursor = collection.find(
                search_query,
                {
                    "content": 1,
                    "metadata": 1,
                    "stored_at": 1,
                    "score": {"$meta": "textScore"},
                    "_id": 0
                }
            ).sort([("score", {"$meta": "textScore"})]).limit(top_k)

            results = await cursor.to_list(length=top_k)
            
            logger.info(f"Text search completed: {len(results)} results found")
            return results

        except Exception as e:
            error_msg = f"Error performing text search: {str(e)}"
            logger.error(error_msg)
            return []
