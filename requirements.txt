pandas>=2.1.1
pathlib>=1.0.1

# Core FastAPI and web framework
fastapi>=0.104.0
uvicorn>=0.23.2
pydantic>=2.4.2

# Configuration and environment
pyyaml>=6.0.1
python-dotenv>=1.0.0

# LangChain core 
langchain>=0.0.335
langchain-openai>=0.0.2
langgraph>=0.0.20
langchain-community>=0.0.10
langchain-chroma>=0.1.0

# Web Search
tavily-python>=0.2.2

# Ui
gradio>=5.0.0

# HTTP requests
requests>=2.31.0

# Document processing
pypdf>=3.17.0
docx2txt>=0.8
unstructured>=0.10.0
python-magic>=0.4.27
python-multipart>=0.0.20

# Rate limiting
slowapi>=0.1.9

# Database connections
pymongo>=4.5.0
motor>=3.3.0  # Async MongoDB driver
sqlalchemy>=2.0.22

# SQL Database drivers for remote connections
# psycopg2-binary>=2.9.7  # PostgreSQL driver
# pymysql>=1.1.0          # MySQL driver
pyodbc>=4.0.39          # SQL Server driver