"""
MongoDB-based document processing system for the Agentic RAG application.
Handles PDF, Word, DOCX, and text format files with MongoDB storage for embeddings.
"""

import logging
import json
import yaml
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    TextLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredMarkdownLoader,
)
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores.utils import filter_complex_metadata

from app.database import MongoDBClient, EmbeddingStorage

logger = logging.getLogger(__name__)


class MongoDBDocumentProcessor:
    """
    MongoDB-based document processing system for the Agentic RAG application.
    Processes documents and stores embeddings in MongoDB for search functionality.
    """

    SUPPORTED_EXTENSIONS = {
        ".pdf": PyPDFLoader,
        ".docx": Docx2txtLoader,
        ".doc": UnstructuredWordDocumentLoader,
        ".txt": TextLoader,
        ".md": UnstructuredMarkdownLoader,
    }

    def __init__(
        self,
        data_dir: str = None,
        embedding_model: str = None,
        chunk_size: int = None,
        chunk_overlap: int = None,
        mongodb_config_file: str = "configs/mongodb_config.yaml",
        document_config_file: str = "configs/document_processing.yaml",
        connection_string: str = None,
        database_name: str = None,
    ):
        """
        Initialize the MongoDB document processor.

        Args:
            data_dir: Base directory for document storage
            embedding_model: OpenAI embedding model to use
            chunk_size: Size of text chunks for splitting
            chunk_overlap: Overlap between chunks
            mongodb_config_file: Path to MongoDB configuration file
            document_config_file: Path to document processing configuration file
            connection_string: MongoDB connection string (overrides config)
            database_name: MongoDB database name (overrides config)
        """
        # Load configurations
        self.mongodb_config = self._load_config(mongodb_config_file)
        self.document_config = self._load_config(document_config_file)

        # MongoDB settings
        mongodb_settings = self.mongodb_config.get("mongodb", {})
        self.connection_string = connection_string or mongodb_settings.get(
            "connection_string", "mongodb://localhost:27017"
        )
        self.database_name = database_name or mongodb_settings.get(
            "database_name", "agentic_rag"
        )

        # Document processing settings
        doc_settings = self.document_config.get("default_settings", {})
        self.data_dir = Path(data_dir or doc_settings.get("data_dir", "data"))
        self.embedding_model = embedding_model or doc_settings.get(
            "embedding_model", "text-embedding-3-small"
        )
        self.chunk_size = chunk_size or doc_settings.get("chunk_size", 1000)
        self.chunk_overlap = chunk_overlap or doc_settings.get("chunk_overlap", 200)

        # Load source directories from config
        source_dirs = self.document_config.get("source_directories", {})
        self.default_source_dir = Path(
            source_dirs.get("default_source_dir", "data/all_docs")
        )
        self.academic_source_dir = Path(
            source_dirs.get("academic_source_dir", "data/academic_docs")
        )
        self.student_source_dir = Path(
            source_dirs.get("student_source_dir", "data/student_docs")
        )
        self.admin_source_dir = Path(
            source_dirs.get("admin_source_dir", "data/admin_docs")
        )
        self.atlasiq_source_dir = Path(
            source_dirs.get("atlasiq_source_dir", "data/atlasiq_docs")
        )
        self.recursive_processing = source_dirs.get("recursive_processing", True)

        # Create directory structure
        self._create_directories()

        # Initialize MongoDB client and embedding storage
        self.mongodb_client = MongoDBClient(
            connection_string=self.connection_string,
            database_name=self.database_name,
            timeout=mongodb_settings.get("timeout", 5000),
        )

        # Initialize components
        self.embeddings = OpenAIEmbeddings(model=self.embedding_model)
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""],
        )

        # Initialize metadata tracking
        self.metadata_file = self.data_dir / "mongodb_processing_metadata.json"
        self.processing_metadata = self._load_metadata()

        logger.info(
            f"MongoDBDocumentProcessor initialized with data_dir: {self.data_dir}"
        )
        logger.info(f"MongoDB connection: {self.connection_string}")
        logger.info(f"Database: {self.database_name}")

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        config_path = Path(config_file)
        if not config_path.exists():
            logger.warning(f"Config file not found: {config_file}, using defaults")
            return {}

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                logger.info(f"Loaded configuration from: {config_file}")
                return config or {}
        except Exception as e:
            logger.error(f"Error loading config from {config_file}: {e}")
            return {}

    def _create_directories(self) -> None:
        """Create necessary directory structure."""
        directories = [
            self.data_dir,
            self.data_dir / "raw",
            self.data_dir / "processed",
            self.default_source_dir,
            self.academic_source_dir,
            self.student_source_dir,
            self.admin_source_dir,
            self.atlasiq_source_dir,
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created directory: {directory}")

    def _load_metadata(self) -> Dict[str, Any]:
        """Load processing metadata from file."""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading metadata: {e}")
                return {}
        return {}

    def _save_metadata(self) -> None:
        """Save processing metadata to file."""
        try:
            with open(self.metadata_file, "w", encoding="utf-8") as f:
                json.dump(self.processing_metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving metadata: {e}")

    def _get_document_loader(self, file_path: Path) -> Optional[Any]:
        """Get appropriate document loader for file type."""
        extension = file_path.suffix.lower()
        loader_class = self.SUPPORTED_EXTENSIONS.get(extension)

        if not loader_class:
            logger.warning(f"Unsupported file type: {extension}")
            return None

        try:
            if extension == ".txt":
                return loader_class(str(file_path), encoding="utf-8")
            elif extension == ".md":
                return loader_class(str(file_path), encoding="utf-8")
            else:
                return loader_class(str(file_path))
        except Exception as e:
            logger.error(f"Error creating loader for {file_path}: {e}")
            return None

    def _get_collection_name(self, source_type: str = "default") -> str:
        """Get MongoDB collection name based on source type."""
        collection_mapping = {
            "default": "document_embeddings",
            "academic": "academic_embeddings",
            "student": "student_embeddings",
            "admin": "admin_embeddings",
            "atlasiq": "atlasiq_embeddings",
        }

        # Check if custom collection names are defined in config
        collections_config = self.mongodb_config.get("collections", {})
        if source_type in collections_config:
            return collections_config[source_type].get(
                "name", collection_mapping.get(source_type, "document_embeddings")
            )

        return collection_mapping.get(source_type, "document_embeddings")

    async def process_document(
        self,
        file_path: str,
        collection_name: str = None,
        source_type: str = "default",
        custom_metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a single document and store embeddings in MongoDB.

        Args:
            file_path: Path to the document to process
            collection_name: Name of the MongoDB collection (overrides source_type)
            source_type: Type of source document (default, academic, student, admin, atlasiq)
            custom_metadata: Additional metadata to attach to document chunks

        Returns:
            Dictionary containing processing results
        """
        file_path = Path(file_path)

        if not file_path.exists():
            error_msg = f"File not found: {file_path}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

        try:
            # Determine collection name
            if not collection_name:
                collection_name = self._get_collection_name(source_type)

            # Get document loader
            loader = self._get_document_loader(file_path)
            if not loader:
                error_msg = f"Unsupported file type: {file_path.suffix}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # Load document
            logger.info(f"Loading document: {file_path}")
            documents = loader.load()

            if not documents:
                error_msg = f"No content extracted from: {file_path}"
                logger.warning(error_msg)
                return {"success": False, "error": error_msg}

            # Split documents into chunks
            logger.info(f"Splitting document into chunks: {file_path}")
            chunks = self.text_splitter.split_documents(documents)

            # Add metadata to chunks
            for i, chunk in enumerate(chunks):
                chunk.metadata.update(
                    {
                        "source_file": str(file_path),
                        "file_name": file_path.name,
                        "file_extension": file_path.suffix,
                        "chunk_index": i,
                        "total_chunks": len(chunks),
                        "processed_at": datetime.now().isoformat(),
                        "processor_version": "2.0.0",
                        "source_type": source_type,
                        "collection_name": collection_name,
                    }
                )

                if custom_metadata:
                    # Filter custom metadata to only include simple types
                    filtered_custom_metadata = {}
                    for key, value in custom_metadata.items():
                        if isinstance(value, (str, int, float, bool)) or value is None:
                            filtered_custom_metadata[key] = value
                        elif isinstance(value, dict) and not value:
                            continue
                        else:
                            filtered_custom_metadata[key] = str(value)

                    chunk.metadata.update(filtered_custom_metadata)

            # Filter complex metadata before storing
            filtered_chunks = filter_complex_metadata(chunks)

            # Initialize embedding storage
            embedding_storage = EmbeddingStorage(
                mongodb_client=self.mongodb_client,
                embedding_model=self.embedding_model,
                collection_name=collection_name,
            )

            # Store documents with embeddings in MongoDB
            logger.info(
                f"Storing {len(filtered_chunks)} chunks in MongoDB collection: {collection_name}"
            )
            storage_result = await embedding_storage.store_documents(filtered_chunks)

            if not storage_result["success"]:
                return {"success": False, "error": storage_result["error"]}

            # Update metadata tracking
            file_key = str(file_path)
            self.processing_metadata[file_key] = {
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "processed_at": datetime.now().isoformat(),
                "collection_name": collection_name,
                "source_type": source_type,
                "chunk_count": len(chunks),
                "stored_count": storage_result["stored_count"],
                "failed_count": storage_result["failed_count"],
                "status": "success",
            }
            self._save_metadata()

            result = {
                "success": True,
                "file_path": str(file_path),
                "collection_name": collection_name,
                "source_type": source_type,
                "chunk_count": len(chunks),
                "stored_count": storage_result["stored_count"],
                "failed_count": storage_result["failed_count"],
                "total_characters": sum(len(chunk.page_content) for chunk in chunks),
            }

            logger.info(f"Successfully processed document: {file_path}")
            return result

        except Exception as e:
            error_msg = f"Error processing document {file_path}: {str(e)}"
            logger.error(error_msg)

            # Update metadata with error
            file_key = str(file_path)
            self.processing_metadata[file_key] = {
                "file_name": file_path.name,
                "processed_at": datetime.now().isoformat(),
                "status": "error",
                "error": str(e),
            }
            self._save_metadata()

            return {"success": False, "error": error_msg}

    async def process_directory(
        self,
        directory_path: str,
        collection_name: str = None,
        source_type: str = "default",
        custom_metadata: Optional[Dict[str, Any]] = None,
        recursive: bool = True,
    ) -> Dict[str, Any]:
        """
        Process all supported documents in a directory.

        Args:
            directory_path: Path to directory containing documents
            collection_name: Name of the MongoDB collection
            source_type: Type of source document
            custom_metadata: Additional metadata to attach to document chunks
            recursive: Whether to process subdirectories

        Returns:
            Dictionary containing batch processing results
        """
        directory_path = Path(directory_path)

        if not directory_path.exists() or not directory_path.is_dir():
            error_msg = f"Directory not found: {directory_path}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

        # Find all supported files
        pattern = "**/*" if recursive else "*"
        all_files = list(directory_path.glob(pattern))

        supported_files = [
            f
            for f in all_files
            if f.is_file() and f.suffix.lower() in self.SUPPORTED_EXTENSIONS
        ]

        if not supported_files:
            error_msg = f"No supported files found in: {directory_path}"
            logger.warning(error_msg)
            return {"success": False, "error": error_msg}

        logger.info(f"Found {len(supported_files)} supported files to process")

        # Process each file
        results = []
        successful_count = 0
        failed_count = 0

        for file_path in supported_files:
            logger.info(
                f"Processing file {successful_count + failed_count + 1}/{len(supported_files)}: {file_path}"
            )

            result = await self.process_document(
                str(file_path),
                collection_name,
                source_type,
                custom_metadata,
            )

            results.append(result)

            if result["success"]:
                successful_count += 1
            else:
                failed_count += 1

        batch_result = {
            "success": True,
            "directory_path": str(directory_path),
            "collection_name": collection_name
            or self._get_collection_name(source_type),
            "source_type": source_type,
            "total_files": len(supported_files),
            "successful_count": successful_count,
            "failed_count": failed_count,
            "results": results,
        }

        logger.info(
            f"Batch processing completed: {successful_count} successful, {failed_count} failed"
        )
        return batch_result

    async def process_source_directory_by_type(
        self,
        source_type: str,
        collection_name: str = None,
        custom_metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process documents from a specific source directory type.

        Args:
            source_type: Type of source directory ('default', 'academic', 'student', 'admin', 'atlasiq')
            collection_name: Name of the MongoDB collection (optional)
            custom_metadata: Additional metadata to attach to document chunks

        Returns:
            Dictionary containing processing results
        """
        source_dir_map = {
            "default": self.default_source_dir,
            "academic": self.academic_source_dir,
            "student": self.student_source_dir,
            "admin": self.admin_source_dir,
            "atlasiq": self.atlasiq_source_dir,
        }

        if source_type not in source_dir_map:
            error_msg = f"Invalid source type: {source_type}. Valid types: {list(source_dir_map.keys())}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

        source_dir = source_dir_map[source_type]
        logger.info(
            f"Processing documents from {source_type} source directory: {source_dir}"
        )

        # Create source directory if it doesn't exist
        source_dir.mkdir(parents=True, exist_ok=True)

        return await self.process_directory(
            directory_path=str(source_dir),
            collection_name=collection_name,
            source_type=source_type,
            custom_metadata=custom_metadata,
            recursive=self.recursive_processing,
        )

    def get_processing_status(self, file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Get processing status for files.

        Args:
            file_path: Specific file path to check, or None for all files

        Returns:
            Dictionary containing processing status information
        """
        if file_path:
            file_key = str(Path(file_path))
            return self.processing_metadata.get(file_key, {"status": "not_processed"})

        return {
            "total_files": len(self.processing_metadata),
            "successful": len(
                [
                    m
                    for m in self.processing_metadata.values()
                    if m.get("status") == "success"
                ]
            ),
            "failed": len(
                [
                    m
                    for m in self.processing_metadata.values()
                    if m.get("status") == "error"
                ]
            ),
            "files": self.processing_metadata,
        }

    def get_source_directories(self) -> Dict[str, str]:
        """
        Get all configured source directories.

        Returns:
            Dictionary mapping source types to directory paths
        """
        return {
            "default": str(self.default_source_dir),
            "academic": str(self.academic_source_dir),
            "student": str(self.student_source_dir),
            "admin": str(self.admin_source_dir),
            "atlasiq": str(self.atlasiq_source_dir),
        }

    async def get_collection_info(
        self, collection_name: str = None, source_type: str = "default"
    ) -> Dict[str, Any]:
        """
        Get information about a specific collection.

        Args:
            collection_name: Name of the collection (optional)
            source_type: Source type to determine collection name

        Returns:
            Dictionary containing collection information
        """
        if not collection_name:
            collection_name = self._get_collection_name(source_type)

        try:
            embedding_storage = EmbeddingStorage(
                mongodb_client=self.mongodb_client,
                embedding_model=self.embedding_model,
                collection_name=collection_name,
            )

            return await embedding_storage.get_collection_info()

        except Exception as e:
            logger.error(f"Error getting collection info for {collection_name}: {e}")
            return {
                "collection_name": collection_name,
                "error": str(e),
            }

    async def delete_collection(
        self, collection_name: str = None, source_type: str = "default"
    ) -> Dict[str, Any]:
        """
        Delete a collection and its associated data.

        Args:
            collection_name: Name of the collection to delete (optional)
            source_type: Source type to determine collection name

        Returns:
            Dictionary containing deletion results
        """
        if not collection_name:
            collection_name = self._get_collection_name(source_type)

        try:
            # Drop the collection
            success = await self.mongodb_client.drop_collection_async(collection_name)

            if success:
                # Update metadata to remove references to this collection
                updated_metadata = {}
                for file_key, metadata in self.processing_metadata.items():
                    if metadata.get("collection_name") != collection_name:
                        updated_metadata[file_key] = metadata

                self.processing_metadata = updated_metadata
                self._save_metadata()

                return {
                    "success": True,
                    "collection_name": collection_name,
                    "message": f"Collection {collection_name} deleted successfully",
                }
            else:
                return {
                    "success": False,
                    "collection_name": collection_name,
                    "error": "Failed to delete collection",
                }

        except Exception as e:
            error_msg = f"Error deleting collection {collection_name}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
            }

    async def search_documents(
        self,
        query: str,
        collection_name: str = None,
        source_type: str = "default",
        top_k: int = 5,
        filter_dict: Optional[Dict[str, Any]] = None,
        score_threshold: float = 0.0,
        search_type: str = "similarity",
    ) -> Dict[str, Any]:
        """
        Search documents in a collection.

        Args:
            query: Search query
            collection_name: Name of the collection to search (optional)
            source_type: Source type to determine collection name
            top_k: Number of results to return
            filter_dict: Optional metadata filter
            score_threshold: Minimum similarity score threshold
            search_type: Type of search ('similarity' or 'text')

        Returns:
            Dictionary containing search results
        """
        if not collection_name:
            collection_name = self._get_collection_name(source_type)

        try:
            embedding_storage = EmbeddingStorage(
                mongodb_client=self.mongodb_client,
                embedding_model=self.embedding_model,
                collection_name=collection_name,
            )

            if search_type == "similarity":
                results = await embedding_storage.similarity_search(
                    query=query,
                    top_k=top_k,
                    filter_dict=filter_dict,
                    score_threshold=score_threshold,
                )
            elif search_type == "text":
                results = await embedding_storage.text_search(
                    query=query, top_k=top_k, filter_dict=filter_dict
                )
            else:
                return {
                    "success": False,
                    "error": f"Invalid search type: {search_type}. Use 'similarity' or 'text'.",
                }

            return {
                "success": True,
                "query": query,
                "collection_name": collection_name,
                "search_type": search_type,
                "results": results,
                "count": len(results),
            }

        except Exception as e:
            error_msg = f"Error searching collection {collection_name}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
            }

    async def close(self):
        """Close MongoDB connections."""
        await self.mongodb_client.disconnect_async()
        self.mongodb_client.disconnect_sync()
