# MongoDB Integration for Agentic RAG

Bu do<PERSON>ü<PERSON>yon, Agentic RAG uygulamasının MongoDB entegrasyonunu açıklar. MongoDB entegrasyonu, dök<PERSON>man embeddinglerini ve metadatalarını MongoDB'de saklamak ve arama yapmak için geliştirilmiştir.

## Özellikler

- **MongoDB ile Embedding Storage**: Döküman embeddingleri MongoDB'de saklanır
- **Similarity Search**: Cosine similarity kullanarak vektör araması
- **Text Search**: MongoDB text indexleri kullanarak tam metin araması
- **Multi-Collection Support**: Farklı döküman türleri için ayrı koleksiyonlar
- **Async Operations**: Asenkron MongoDB operasyonları
- **Docker Integration**: MongoDB Docker container desteği
- **Metadata Filtering**: Gelişmiş metadata filtreleme
- **Batch Processing**: Toplu döküman işleme

## Kurulum

### 1. Dependencies

MongoDB entegrasyonu için gerekli paketler `requirements.txt` dosyas<PERSON>na e<PERSON>:

```
pymongo>=4.5.0
motor>=3.3.0  # Async MongoDB driver
```

### 2. Docker ile MongoDB

MongoDB'yi Docker ile çalıştırmak için güncellenmiş docker-compose dosyalarını kullanın:

```bash
# Development için
cd docker
docker-compose -f docker-compose.backend-only.yml up -d

# Production için
docker-compose -f docker-compose.deploy.yml up -d
```

### 3. Konfigürasyon

MongoDB ayarları `configs/mongodb_config.yaml` dosyasında yapılandırılır:

```yaml
mongodb:
  connection_string: "mongodb://mongodb:27017"
  database_name: "agentic_rag"
  timeout: 5000

collections:
  default_embeddings:
    name: "document_embeddings"
  academic_embeddings:
    name: "academic_embeddings"
  # ... diğer koleksiyonlar
```

## Kullanım

### 1. Document Processing

```python
from app.document_processing.mongodb_document_processor import MongoDBDocumentProcessor

# Processor'ı başlat
processor = MongoDBDocumentProcessor(
    connection_string="mongodb://localhost:27017",
    database_name="agentic_rag",
    embedding_model="text-embedding-3-small"
)

# Tek döküman işle
result = await processor.process_document(
    file_path="path/to/document.pdf",
    source_type="academic",
    custom_metadata={"category": "research"}
)

# Dizin işle
result = await processor.process_source_directory_by_type(
    source_type="academic"
)
```

### 2. Document Search

```python
from app.tools.mongodb_document_search import MongoDBDocumentSearchTool

# Search tool'u başlat
search_tool = MongoDBDocumentSearchTool()
search_tool.config = {
    "connection_string": "mongodb://localhost:27017",
    "database_name": "agentic_rag",
    "collection_name": "document_embeddings",
    "embedding_model": "text-embedding-3-small"
}
search_tool.initialize()

# Similarity search
result = await search_tool.execute(
    query="MongoDB features",
    top_k=5,
    search_type="similarity"
)

# Multi-collection search
result = await search_tool.search_multiple_collections(
    query="document processing",
    source_types=["academic", "student"],
    top_k_per_collection=3
)
```

### 3. Collection Management

```python
# Collection bilgisi al
info = await processor.get_collection_info(source_type="academic")

# Collection sil
result = await processor.delete_collection(source_type="academic")

# Processing durumu kontrol et
status = processor.get_processing_status()
```

## Koleksiyon Yapısı

Her döküman MongoDB'de şu yapıda saklanır:

```json
{
  "content": "Döküman içeriği...",
  "metadata": {
    "source_file": "/path/to/file.pdf",
    "file_name": "file.pdf",
    "file_extension": ".pdf",
    "chunk_index": 0,
    "total_chunks": 5,
    "processed_at": "2024-01-01T12:00:00",
    "source_type": "academic",
    "collection_name": "academic_embeddings"
  },
  "embedding": [0.1, 0.2, 0.3, ...],
  "stored_at": "2024-01-01T12:00:00",
  "embedding_model": "text-embedding-3-small"
}
```

## Source Types ve Collections

| Source Type | Collection Name | Açıklama |
|-------------|----------------|----------|
| default | document_embeddings | Genel dökümanlar |
| academic | academic_embeddings | Akademik dökümanlar |
| student | student_embeddings | Öğrenci dökümanları |
| admin | admin_embeddings | Yönetim dökümanları |
| atlasiq | atlasiq_embeddings | AtlasIQ dökümanları |

## Environment Variables

```bash
# MongoDB bağlantı ayarları
MONGODB_CONNECTION_STRING=mongodb://mongodb:27017
MONGODB_DATABASE=agentic_rag
MONGODB_USERNAME=admin
MONGODB_PASSWORD=password

# OpenAI API Key (embeddings için)
OPENAI_API_KEY=your_openai_api_key
```

## Indexler

MongoDB'de otomatik olarak oluşturulan indexler:

- **Text Index**: `content` alanında tam metin araması için
- **Metadata Indexes**: 
  - `metadata.source_file`
  - `metadata.file_name`
  - `metadata.processed_at`
  - `metadata.chunk_index`
- **Compound Index**: `metadata.source_file` + `metadata.chunk_index`

## Performance Optimizasyonları

1. **Batch Processing**: Dökümanlar batch'ler halinde işlenir
2. **Connection Pooling**: MongoDB connection pool kullanılır
3. **Async Operations**: Tüm operasyonlar asenkron
4. **Indexing**: Optimal arama performansı için indexler
5. **Embedding Caching**: Embedding hesaplamaları optimize edilir

## Örnek Kullanım

Detaylı örnek için `examples/mongodb_document_processing_example.py` dosyasına bakın:

```bash
python examples/mongodb_document_processing_example.py
```

## Troubleshooting

### MongoDB Bağlantı Sorunları

```bash
# MongoDB container'ının çalıştığını kontrol edin
docker ps | grep mongodb

# MongoDB loglarını kontrol edin
docker logs agentic-rag-mongodb

# MongoDB'ye bağlanmayı test edin
docker exec -it agentic-rag-mongodb mongosh
```

### Embedding Sorunları

- OpenAI API key'inin doğru olduğundan emin olun
- Rate limiting durumunda batch size'ı küçültün
- Network bağlantısını kontrol edin

### Performance Sorunları

- MongoDB indexlerinin oluşturulduğunu kontrol edin
- Collection istatistiklerini inceleyin
- Slow query loglarını kontrol edin

## Migration

Mevcut Chroma verilerini MongoDB'ye migrate etmek için:

1. Mevcut Chroma verilerini export edin
2. MongoDB processor ile yeniden işleyin
3. Arama sonuçlarını karşılaştırın

## Backup ve Recovery

```bash
# MongoDB backup
docker exec agentic-rag-mongodb mongodump --out /backup

# MongoDB restore
docker exec agentic-rag-mongodb mongorestore /backup
```
