# docker-compose.deploy.yml
# This file is intended for production deployment.
# It pulls pre-built images from a container registry (e.g., AWS ECR).
version: '3.8'

services:
  # MongoDB Database Service
  mongodb:
    image: mongo:7.0
    container_name: agentic-rag-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
      - MONGO_INITDB_DATABASE=${MONGO_DATABASE:-agentic_rag}
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - agentic-rag-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Agentic RAG Backend Service
  agentic-rag:
    # IMPORTANT: This will be replaced by an environment variable from the .env file.
    # <PERSON> is responsible for setting the IMAGE_URI in the .env file on the server.
    image: ${IMAGE_URI}
    user: root
    container_name: agentic-rag-backend
    restart: unless-stopped
    ports:
      - "3820:8000"
    env_file:
      - .env
    environment:
      # Most variables are in .env. Override or set specific ones if needed.
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false # Should be false in production
      - LOG_LEVEL=INFO
      - LOG_DIR=logs
      # MongoDB connection settings
      - MONGODB_CONNECTION_STRING=mongodb://mongodb:27017
      - MONGODB_DATABASE=${MONGO_DATABASE:-agentic_rag}
      - MONGODB_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGODB_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
    volumes:
      # Mount persistent data volumes from the host
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - agentic-rag-network
    depends_on:
      mongodb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Docker volumes for persistent data
volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

# Docker networks
networks:
  agentic-rag-network:
    driver: bridge
